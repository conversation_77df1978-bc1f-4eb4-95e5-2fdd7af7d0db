# Migration Guide: Upgrading to Command-Line Modes

This guide helps existing users migrate from the legacy single-execution system to the new command-line modes system.

## Table of Contents

1. [Overview of Changes](#overview-of-changes)
2. [Backward Compatibility](#backward-compatibility)
3. [Step-by-Step Migration](#step-by-step-migration)
4. [Configuration Updates](#configuration-updates)
5. [Script and Automation Updates](#script-and-automation-updates)
6. [Testing Your Migration](#testing-your-migration)
7. [Troubleshooting Migration Issues](#troubleshooting-migration-issues)

## Overview of Changes

### What's New

The RL Portfolio Rebalancing System now supports three distinct execution modes:

- **Training Mode** (`--mode=training`): Execute only training phase
- **Evaluation Mode** (`--mode=evaluation`): Execute only evaluation phase  
- **Full Mode** (`--mode=full`): Execute both phases (default, backward compatible)

### Key Benefits

1. **Faster Development**: Train and evaluate separately for quicker iteration
2. **Resource Efficiency**: Use only the resources needed for specific tasks
3. **Better Automation**: Integrate specific phases into CI/CD pipelines
4. **Improved Debugging**: Isolate issues to specific execution phases
5. **Flexible Deployment**: Deploy training and evaluation on different schedules

### What Stays the Same

- **Default Behavior**: Running `python main.py` works exactly as before
- **Output Files**: Same file formats and naming conventions
- **Configuration**: Existing config.py settings remain valid
- **Dependencies**: No new package requirements
- **Data Sources**: Same ETF symbols and data fetching logic

## Backward Compatibility

### 100% Compatible Commands

These commands work identically in both old and new systems:

```bash
# These commands produce identical results
python main.py                    # Old and new: runs complete workflow
python main.py > output.log       # Old and new: same logging behavior
python main.py 2>&1 | tee log.txt # Old and new: same output handling
```

### Enhanced Commands (Optional Upgrades)

These commands provide new functionality but aren't required:

```bash
# New optional enhancements
python main.py --mode=full        # Explicit full mode (same as default)
python main.py --help             # New help information
python main.py --mode=training    # New: training only
python main.py --mode=evaluation  # New: evaluation only
```

## Step-by-Step Migration

### Phase 1: Verify Current Setup (No Changes Required)

1. **Test Current Functionality:**
   ```bash
   # Ensure your current setup still works
   python main.py
   ```

2. **Verify Output Files:**
   ```bash
   # Check that all expected files are generated
   ls -la results/
   ls -la models/
   ls -la logs/
   ```

3. **Document Current Behavior:**
   ```bash
   # Record current execution time for comparison
   time python main.py
   ```

### Phase 2: Explore New Modes (Optional)

1. **Try Training Mode:**
   ```bash
   # Run only training phase
   python main.py --mode=training
   
   # Verify model is saved
   ls -la models/
   ```

2. **Try Evaluation Mode:**
   ```bash
   # Run only evaluation phase (requires existing model)
   python main.py --mode=evaluation
   
   # Verify CSV files are generated
   ls -la results/
   ```

3. **Compare Performance:**
   ```bash
   # Time individual modes
   time python main.py --mode=training
   time python main.py --mode=evaluation
   
   # Compare to full mode
   time python main.py --mode=full
   ```

### Phase 3: Update Scripts (Recommended)

1. **Update Execution Scripts:**
   ```bash
   # Old script (still works)
   #!/bin/bash
   python main.py

   # Enhanced script (recommended)
   #!/bin/bash
   python main.py --mode=full
   ```

2. **Add Error Handling:**
   ```bash
   #!/bin/bash
   # Enhanced script with error handling
   if python main.py --mode=full; then
       echo "Execution completed successfully"
   else
       echo "Execution failed with exit code $?"
       exit 1
   fi
   ```

3. **Add Mode Selection:**
   ```bash
   #!/bin/bash
   # Script with mode selection
   MODE=${1:-full}
   echo "Running in $MODE mode"
   python main.py --mode=$MODE
   ```

### Phase 4: Leverage New Features (Optional)

1. **Use Environment Variables:**
   ```bash
   # Set default mode via environment
   export RL_PORTFOLIO_MODE=full
   python main.py  # Uses environment setting
   ```

2. **Optimize for Development:**
   ```bash
   # Development workflow
   python main.py --mode=training    # Develop model
   python main.py --mode=evaluation  # Test model
   ```

3. **Optimize for Production:**
   ```bash
   # Production workflow
   export RL_PORTFOLIO_MODE=full
   export RL_PORTFOLIO_OUTPUT_DIR=/var/results
   python main.py
   ```

## Configuration Updates

### Legacy Configuration (Still Supported)

```python
# config.py - Old pattern (still works)
from config import TRADING_CONFIG, DATA_CONFIG

initial_cash = TRADING_CONFIG["initial_cash"]
etf_symbols = DATA_CONFIG["etf_symbols"]
```

### New Configuration (Recommended)

```python
# config.py - New pattern (recommended)
from config import ConfigManager

# Create mode-aware configuration
config = ConfigManager("full")  # or "training", "evaluation"

# Access configuration
all_config = config.get_all_config()
initial_cash = all_config["trading"]["initial_cash"]
etf_symbols = all_config["data"]["etf_symbols"]

# Mode-specific settings
should_generate_csv = config.should_generate_csv
should_skip_training = config.should_skip_training
```

### Gradual Migration Approach

```python
# Hybrid approach - migrate gradually
from config import CONFIG, ConfigManager

# Use legacy for existing code
initial_cash = CONFIG.TRADING.initial_cash

# Use new system for new features
config = ConfigManager("training")
if config.should_skip_evaluation:
    print("Skipping evaluation in training mode")
```

## Script and Automation Updates

### Cron Jobs

**Before:**
```bash
# Old cron job
0 2 1 * * cd /path/to/rl_portfolio && python main.py
```

**After (Backward Compatible):**
```bash
# New cron job (same behavior)
0 2 1 * * cd /path/to/rl_portfolio && python main.py --mode=full

# Or use environment variable
0 2 1 * * cd /path/to/rl_portfolio && RL_PORTFOLIO_MODE=full python main.py
```

### Docker Integration

**Before:**
```dockerfile
# Old Dockerfile
FROM python:3.9-slim
WORKDIR /app
COPY . .
RUN pip install -r requirements.txt
CMD ["python", "main.py"]
```

**After (Enhanced):**
```dockerfile
# Enhanced Dockerfile
FROM python:3.9-slim
WORKDIR /app
COPY . .
RUN pip install -r requirements.txt

# Set default mode (backward compatible)
ENV RL_PORTFOLIO_MODE=full
ENV RL_PORTFOLIO_UNICODE_MODE=ascii

CMD ["python", "main.py"]
```

### CI/CD Pipelines

**Before:**
```yaml
# Old CI pipeline
- name: Run RL Portfolio System
  run: python main.py
```

**After (Enhanced):**
```yaml
# Enhanced CI pipeline
- name: Run Training
  run: python main.py --mode=training
  
- name: Run Evaluation
  run: python main.py --mode=evaluation
  
- name: Upload Results
  uses: actions/upload-artifact@v2
  with:
    name: results
    path: results/
```

### Monitoring Scripts

**Before:**
```bash
#!/bin/bash
# Old monitoring script
python main.py > /var/log/rl_portfolio.log 2>&1
```

**After (Enhanced):**
```bash
#!/bin/bash
# Enhanced monitoring script
MODE=${RL_PORTFOLIO_MODE:-full}
LOG_FILE="/var/log/rl_portfolio_${MODE}_$(date +%Y%m%d).log"

echo "Starting RL Portfolio in $MODE mode" >> $LOG_FILE
if python main.py --mode=$MODE >> $LOG_FILE 2>&1; then
    echo "Execution completed successfully" >> $LOG_FILE
else
    echo "Execution failed with exit code $?" >> $LOG_FILE
    # Send alert notification
    echo "RL Portfolio execution failed" | mail -s "Alert" <EMAIL>
fi
```

## Testing Your Migration

### Validation Checklist

1. **Functional Testing:**
   ```bash
   # Test all modes produce expected outputs
   python main.py --mode=training
   ls models/  # Should contain new model file
   
   python main.py --mode=evaluation
   ls results/ # Should contain CSV files
   
   python main.py --mode=full
   # Should contain outputs from both phases
   ```

2. **Performance Testing:**
   ```bash
   # Compare execution times
   time python main.py --mode=training
   time python main.py --mode=evaluation
   time python main.py --mode=full
   
   # Verify full mode time ≈ training + evaluation
   ```

3. **Compatibility Testing:**
   ```bash
   # Ensure legacy commands still work
   python main.py
   
   # Compare outputs with explicit full mode
   python main.py --mode=full
   
   # Outputs should be identical
   ```

4. **Error Handling Testing:**
   ```bash
   # Test invalid arguments
   python main.py --mode=invalid  # Should show error
   
   # Test missing model for evaluation
   rm models/*.zip
   python main.py --mode=evaluation  # Should show error
   ```

### Automated Testing Script

```bash
#!/bin/bash
# migration_test.sh - Automated migration testing

set -e

echo "=== Migration Testing ==="

# Test 1: Legacy compatibility
echo "Testing legacy compatibility..."
python main.py > legacy_output.log 2>&1
echo "✓ Legacy execution completed"

# Test 2: Full mode equivalence
echo "Testing full mode equivalence..."
python main.py --mode=full > full_mode_output.log 2>&1
echo "✓ Full mode execution completed"

# Test 3: Individual modes
echo "Testing individual modes..."
python main.py --mode=training > training_output.log 2>&1
echo "✓ Training mode completed"

python main.py --mode=evaluation > evaluation_output.log 2>&1
echo "✓ Evaluation mode completed"

# Test 4: Output validation
echo "Validating outputs..."
if [ -d "models" ] && [ -d "results" ] && [ -d "logs" ]; then
    echo "✓ All output directories created"
else
    echo "✗ Missing output directories"
    exit 1
fi

# Test 5: File counts
model_count=$(ls models/*.zip 2>/dev/null | wc -l)
result_count=$(ls results/*.csv 2>/dev/null | wc -l)

if [ $model_count -gt 0 ] && [ $result_count -gt 0 ]; then
    echo "✓ Output files generated"
else
    echo "✗ Missing output files"
    exit 1
fi

echo "=== All Tests Passed ==="
```

## Troubleshooting Migration Issues

### Common Issues and Solutions

1. **"Command not recognized" Error:**
   ```bash
   # Issue: Old Python version doesn't support argparse features
   python --version  # Check version
   
   # Solution: Upgrade Python or use legacy mode
   python main.py  # Works without arguments
   ```

2. **"Mode validation failed" Error:**
   ```bash
   # Issue: Missing models directory for evaluation mode
   mkdir -p models
   python main.py --mode=training  # Create a model first
   python main.py --mode=evaluation
   ```

3. **"Output files missing" Issue:**
   ```bash
   # Issue: Permissions or disk space
   ls -la results/ models/ logs/
   df -h  # Check disk space
   chmod 755 results/ models/ logs/  # Fix permissions
   ```

4. **"Performance degradation" Issue:**
   ```bash
   # Issue: Running full mode when only evaluation needed
   # Solution: Use appropriate mode
   python main.py --mode=evaluation  # Faster for backtesting only
   ```

### Rollback Plan

If you encounter issues, you can easily rollback:

```bash
# Rollback to legacy behavior
# Simply use the old command - no changes needed
python main.py

# Or explicitly disable new features
export RL_PORTFOLIO_MODE=full
python main.py
```

### Getting Help

1. **Check Documentation:**
   ```bash
   python main.py --help
   cat docs/mode-configuration.md
   cat docs/mode-troubleshooting.md
   ```

2. **Run Diagnostics:**
   ```bash
   # Check system health
   python -c "from config import ConfigManager; print(ConfigManager('full'))"
   
   # Validate configuration
   python -c "
   from config import ConfigManager
   config = ConfigManager('full')
   is_valid, errors = config.validate_mode_requirements()
   print(f'Valid: {is_valid}, Errors: {errors}')
   "
   ```

3. **Enable Debug Logging:**
   ```bash
   export RL_PORTFOLIO_UNICODE_DEBUG=true
   python main.py --mode=full
   ```

## Migration Timeline Recommendation

### Week 1: Assessment and Testing
- Test current system with new version
- Verify backward compatibility
- Run migration testing script

### Week 2: Gradual Adoption
- Update development scripts to use explicit modes
- Begin using training/evaluation modes for development
- Update documentation and team training

### Week 3: Production Integration
- Update production scripts with explicit `--mode=full`
- Add environment variable configurations
- Update monitoring and alerting systems

### Week 4: Optimization
- Optimize workflows using appropriate modes
- Update CI/CD pipelines
- Implement advanced features like custom data splits

## Summary

The migration to command-line modes is designed to be seamless and optional. Your existing workflows will continue to work without any changes, while new features are available when you're ready to adopt them. The key principle is **backward compatibility first, enhancement second**.

Key takeaways:
- ✅ **No immediate action required** - existing scripts continue to work
- ✅ **Gradual migration** - adopt new features at your own pace  
- ✅ **Easy rollback** - can always return to legacy behavior
- ✅ **Enhanced functionality** - new modes provide better control and performance
- ✅ **Future-proof** - system designed for long-term maintainability

For additional support, refer to the troubleshooting guide or run the diagnostic commands provided in this document.