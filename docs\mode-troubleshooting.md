# Mode-Related Troubleshooting Guide

This document provides solutions for common issues encountered when using different execution modes of the RL Portfolio Rebalancing System.

## Table of Contents

1. [Common Error Messages](#common-error-messages)
2. [Mode-Specific Issues](#mode-specific-issues)
3. [Data and Model Problems](#data-and-model-problems)
4. [Environment and Configuration Issues](#environment-and-configuration-issues)
5. [Performance and Resource Issues](#performance-and-resource-issues)
6. [Integration and Automation Issues](#integration-and-automation-issues)

## Common Error Messages

### "Invalid mode argument"

**Error Message:**
```
ARGUMENT ERROR
================================================================================
Invalid mode argument: 'train'
Valid modes: training, evaluation, full
```

**Cause:** Incorrect mode name provided to `--mode` argument.

**Solution:**
```bash
# Incorrect
python main.py --mode=train

# Correct
python main.py --mode=training
```

**Valid modes:** `training`, `evaluation`, `full`

### "No trained model exists"

**Error Message:**
```
VALIDATION ERROR
================================================================================
Evaluation mode requires a trained model, but no model was found.
```

**Cause:** Evaluation mode requires an existing model, but none was found.

**Solutions:**

1. **Train a model first:**
   ```bash
   python main.py --mode=training
   python main.py --mode=evaluation
   ```

2. **Specify custom model path:**
   ```bash
   python main.py --mode=evaluation --model-path=path/to/model.zip
   ```

3. **Check models directory:**
   ```bash
   ls -la models/
   # Should contain .zip files
   ```

### "Data split ratio out of range"

**Error Message:**
```
ValueError: Split ratio must be between 0.0 and 1.0, got: 1.2
```

**Cause:** Invalid data split ratio provided.

**Solution:**
```bash
# Incorrect
RL_PORTFOLIO_DATA_SPLIT_RATIO=1.2 python main.py

# Correct (80% training, 20% evaluation)
RL_PORTFOLIO_DATA_SPLIT_RATIO=0.8 python main.py
```

### "Permission denied"

**Error Message:**
```
PERMISSION ERROR
================================================================================
Cannot create output directory results/: Permission denied
```

**Cause:** Insufficient permissions to create directories or write files.

**Solutions:**

1. **Check directory permissions:**
   ```bash
   ls -la
   chmod 755 results/ models/ logs/
   ```

2. **Use custom output directory:**
   ```bash
   mkdir -p ~/rl_results
   RL_PORTFOLIO_OUTPUT_DIR=~/rl_results python main.py --mode=evaluation
   ```

3. **Run with appropriate permissions:**
   ```bash
   # On Unix systems, if necessary
   sudo python main.py --mode=evaluation
   ```

## Mode-Specific Issues

### Training Mode Issues

#### "Training fails with memory error"

**Error Message:**
```
MemoryError: Unable to allocate array with shape (1000000, 256)
```

**Cause:** Insufficient memory for training large models.

**Solutions:**

1. **Reduce data split ratio:**
   ```bash
   RL_PORTFOLIO_DATA_SPLIT_RATIO=0.6 python main.py --mode=training
   ```

2. **Monitor memory usage:**
   ```bash
   # Monitor during execution
   python -m memory_profiler main.py --mode=training
   ```

3. **Close other applications:**
   ```bash
   # Free up system memory before training
   python main.py --mode=training
   ```

#### "TensorBoard not starting"

**Error Message:**
```
WARNING: TensorBoard logging failed to initialize
```

**Cause:** TensorBoard dependencies missing or port conflicts.

**Solutions:**

1. **Install TensorBoard:**
   ```bash
   pip install tensorboard
   ```

2. **Check port availability:**
   ```bash
   # TensorBoard typically uses port 6006
   netstat -an | grep 6006
   ```

3. **Disable TensorBoard if not needed:**
   ```python
   # In config.py, modify MODE_CONFIG
   "enable_tensorboard": False
   ```

### Evaluation Mode Issues

#### "Model loading fails"

**Error Message:**
```
FileNotFoundError: Model file not found: models/nonexistent_model.zip
```

**Cause:** Specified model file doesn't exist or path is incorrect.

**Solutions:**

1. **List available models:**
   ```bash
   ls -la models/
   ```

2. **Use auto-detection:**
   ```bash
   # Don't specify --model-path, let system auto-detect
   python main.py --mode=evaluation
   ```

3. **Check model file integrity:**
   ```bash
   # Verify model file is not corrupted
   file models/your_model.zip
   unzip -t models/your_model.zip
   ```

#### "Evaluation data insufficient"

**Error Message:**
```
ValueError: Evaluation data contains only 50 records, minimum required: 200
```

**Cause:** Not enough data for meaningful evaluation.

**Solutions:**

1. **Adjust data split ratio:**
   ```bash
   # Give more data to evaluation (25% instead of 20%)
   RL_PORTFOLIO_DATA_SPLIT_RATIO=0.75 python main.py --mode=evaluation
   ```

2. **Check data availability:**
   ```bash
   # Verify data files exist and contain sufficient records
   ls -la data/
   ```

3. **Use full mode for complete dataset:**
   ```bash
   python main.py --mode=full
   ```

### Full Mode Issues

#### "Execution takes too long"

**Cause:** Full mode runs both training and evaluation, which can be time-consuming.

**Solutions:**

1. **Use separate modes for development:**
   ```bash
   # Faster development cycle
   python main.py --mode=training
   python main.py --mode=evaluation
   ```

2. **Optimize data split:**
   ```bash
   # Use less training data for faster execution
   RL_PORTFOLIO_DATA_SPLIT_RATIO=0.75 python main.py --mode=full
   ```

3. **Monitor progress:**
   ```bash
   # Run with verbose output
   python main.py --mode=full 2>&1 | tee execution.log
   ```

## Data and Model Problems

### Data Fetching Issues

#### "Yahoo Finance connection error"

**Error Message:**
```
NETWORK ERROR
================================================================================
Failed to fetch data from Yahoo Finance: Connection timeout
```

**Cause:** Network connectivity issues or Yahoo Finance rate limiting.

**Solutions:**

1. **Check internet connection:**
   ```bash
   ping finance.yahoo.com
   ```

2. **Retry after delay:**
   ```bash
   # Wait and retry
   sleep 60
   python main.py --mode=training
   ```

3. **Use cached data if available:**
   ```bash
   # Check if data directory has cached files
   ls -la data/
   ```

#### "Missing ETF data"

**Error Message:**
```
KeyError: 'VT' not found in fetched data
```

**Cause:** One or more ETF symbols failed to fetch data.

**Solutions:**

1. **Check ETF symbols:**
   ```python
   # Verify symbols in config.py
   DATA_CONFIG = {
       "etf_symbols": ["VT", "IEF", "REET", "GLD", "DBA", "USO", "UUP"]
   }
   ```

2. **Test individual symbol:**
   ```python
   import yfinance as yf
   ticker = yf.Ticker("VT")
   data = ticker.history(period="1y")
   print(data.head())
   ```

3. **Check symbol validity:**
   ```bash
   # Verify symbols are still valid on Yahoo Finance
   curl "https://finance.yahoo.com/quote/VT"
   ```

### Model Compatibility Issues

#### "Model version mismatch"

**Error Message:**
```
RuntimeError: Model was trained with different TensorTrade version
```

**Cause:** Model was created with incompatible software versions.

**Solutions:**

1. **Check software versions:**
   ```bash
   pip list | grep -E "(tensortrade|stable-baselines3)"
   ```

2. **Retrain model with current versions:**
   ```bash
   python main.py --mode=training
   ```

3. **Use version-specific environments:**
   ```bash
   # Create environment for specific versions
   conda create -n rl_portfolio_v1 python=3.9
   conda activate rl_portfolio_v1
   pip install -r requirements.txt
   ```

## Environment and Configuration Issues

### Environment Variable Problems

#### "Environment variable not recognized"

**Error Message:**
```
WARNING: Invalid mode in RL_PORTFOLIO_MODE: 'TRAINING'
```

**Cause:** Environment variable value is case-sensitive or invalid.

**Solutions:**

1. **Use correct case:**
   ```bash
   # Incorrect
   export RL_PORTFOLIO_MODE=TRAINING

   # Correct
   export RL_PORTFOLIO_MODE=training
   ```

2. **Verify environment variables:**
   ```bash
   env | grep RL_PORTFOLIO
   ```

3. **Clear invalid variables:**
   ```bash
   unset RL_PORTFOLIO_MODE
   python main.py --mode=training
   ```

#### "Configuration validation failed"

**Error Message:**
```
WARNING: Invalid data_split_ratio '0.95' for mode 'evaluation'
```

**Cause:** Configuration values are outside valid ranges.

**Solutions:**

1. **Check valid ranges:**
   ```bash
   # Data split ratio must be between 0.0 and 1.0
   RL_PORTFOLIO_DATA_SPLIT_RATIO=0.8 python main.py
   ```

2. **Reset to defaults:**
   ```bash
   # Remove environment overrides
   unset RL_PORTFOLIO_DATA_SPLIT_RATIO
   python main.py --mode=training
   ```

3. **Validate configuration:**
   ```python
   from config import ConfigManager
   config = ConfigManager("training")
   is_valid, errors = config.validate_mode_requirements()
   print(f"Valid: {is_valid}, Errors: {errors}")
   ```

### Unicode and Logging Issues

#### "Unicode encoding error"

**Error Message:**
```
UnicodeEncodeError: 'cp1252' codec can't encode character
```

**Cause:** Console doesn't support Unicode characters in log output.

**Solutions:**

1. **Use ASCII mode:**
   ```bash
   RL_PORTFOLIO_UNICODE_MODE=ascii python main.py --mode=training
   ```

2. **Use safe mode:**
   ```bash
   RL_PORTFOLIO_UNICODE_MODE=safe python main.py --mode=training
   ```

3. **Configure console encoding:**
   ```bash
   # On Windows
   chcp 65001
   python main.py --mode=training
   ```

## Performance and Resource Issues

### Memory Issues

#### "Out of memory during training"

**Cause:** Training process consumes too much RAM.

**Solutions:**

1. **Reduce training data:**
   ```bash
   RL_PORTFOLIO_DATA_SPLIT_RATIO=0.6 python main.py --mode=training
   ```

2. **Monitor memory usage:**
   ```bash
   # Linux/Mac
   top -p $(pgrep -f "python main.py")
   
   # Windows
   tasklist | findstr python
   ```

3. **Use memory-efficient settings:**
   ```python
   # Modify TRAINING_CONFIG in config.py
   TRAINING_CONFIG = {
       "batch_size": 32,  # Reduce from 64
       "policy_layers": [128, 128],  # Reduce from [256, 256]
   }
   ```

### Disk Space Issues

#### "No space left on device"

**Cause:** Insufficient disk space for outputs.

**Solutions:**

1. **Check disk space:**
   ```bash
   df -h
   ```

2. **Clean old results:**
   ```bash
   # Remove old result files
   find results/ -name "*.csv" -mtime +30 -delete
   find logs/ -name "*.log" -mtime +30 -delete
   ```

3. **Use custom output directory:**
   ```bash
   RL_PORTFOLIO_OUTPUT_DIR=/path/to/larger/disk python main.py --mode=evaluation
   ```

### CPU Performance Issues

#### "Training is very slow"

**Cause:** CPU-intensive operations taking too long.

**Solutions:**

1. **Reduce training timesteps:**
   ```python
   # Modify TRAINING_CONFIG in config.py
   TRAINING_CONFIG = {
       "total_timesteps": 1000,  # Reduce from 2000
   }
   ```

2. **Use parallel processing:**
   ```bash
   # Set number of CPU cores to use
   export OMP_NUM_THREADS=4
   python main.py --mode=training
   ```

3. **Monitor CPU usage:**
   ```bash
   # Linux/Mac
   htop
   
   # Windows
   wmic cpu get loadpercentage /value
   ```

## Integration and Automation Issues

### Script Integration Problems

#### "Exit code not handled properly"

**Cause:** Scripts don't handle different exit codes from the system.

**Solution:**
```bash
#!/bin/bash
python main.py --mode=training
exit_code=$?

case $exit_code in
    0) echo "Success" ;;
    1) echo "General error" ;;
    2) echo "Invalid arguments" ;;
    3) echo "Mode validation failed" ;;
    4) echo "Required files not found" ;;
    5) echo "Import/dependency error" ;;
    6) echo "Network/data fetching error" ;;
    7) echo "Permission error" ;;
    *) echo "Unknown error: $exit_code" ;;
esac

exit $exit_code
```

### Docker Issues

#### "Container fails to start"

**Cause:** Docker environment configuration issues.

**Solutions:**

1. **Check Dockerfile:**
   ```dockerfile
   FROM python:3.9-slim
   WORKDIR /app
   COPY requirements.txt .
   RUN pip install -r requirements.txt
   COPY . .
   ENV RL_PORTFOLIO_UNICODE_MODE=ascii
   CMD ["python", "main.py"]
   ```

2. **Mount volumes correctly:**
   ```bash
   docker run -v $(pwd)/models:/app/models -v $(pwd)/results:/app/results rl-portfolio
   ```

3. **Check container logs:**
   ```bash
   docker logs <container_id>
   ```

### CI/CD Pipeline Issues

#### "Pipeline fails on model validation"

**Cause:** CI environment doesn't have proper setup for model training/evaluation.

**Solutions:**

1. **Use test mode:**
   ```yaml
   # In CI configuration
   env:
     RL_PORTFOLIO_SKIP_VALIDATION: true
     RL_PORTFOLIO_DATA_SPLIT_RATIO: 0.5
   ```

2. **Cache dependencies:**
   ```yaml
   - name: Cache pip dependencies
     uses: actions/cache@v2
     with:
       path: ~/.cache/pip
       key: ${{ runner.os }}-pip-${{ hashFiles('requirements.txt') }}
   ```

3. **Use smaller test datasets:**
   ```python
   # Create test configuration
   TEST_CONFIG = {
       "total_timesteps": 100,  # Much smaller for CI
       "batch_size": 16,
   }
   ```

## Diagnostic Commands

### System Health Check

```bash
#!/bin/bash
# health_check.sh - Comprehensive system diagnostic

echo "=== RL Portfolio System Health Check ==="

# Check Python version
echo "Python version:"
python --version

# Check dependencies
echo "Checking dependencies..."
python -c "
import sys
required = ['pandas', 'numpy', 'yfinance', 'ta', 'tensortrade', 'stable_baselines3', 'matplotlib']
missing = []
for pkg in required:
    try:
        __import__(pkg)
        print(f'✓ {pkg}')
    except ImportError:
        print(f'✗ {pkg} - MISSING')
        missing.append(pkg)

if missing:
    print(f'Missing packages: {missing}')
    sys.exit(1)
else:
    print('All dependencies available')
"

# Check directories
echo "Checking directories..."
for dir in data models logs results; do
    if [ -d "$dir" ]; then
        echo "✓ $dir exists"
    else
        echo "✗ $dir missing - creating..."
        mkdir -p "$dir"
    fi
done

# Check configuration
echo "Checking configuration..."
python -c "
from config import ConfigManager
try:
    config = ConfigManager('training')
    print('✓ Configuration loaded successfully')
    is_valid, errors = config.validate_mode_requirements()
    if is_valid:
        print('✓ Configuration validation passed')
    else:
        print(f'✗ Configuration errors: {errors}')
except Exception as e:
    print(f'✗ Configuration error: {e}')
"

# Test data fetching
echo "Testing data connectivity..."
python -c "
import yfinance as yf
try:
    ticker = yf.Ticker('VT')
    data = ticker.history(period='5d')
    if not data.empty:
        print('✓ Data fetching works')
    else:
        print('✗ Data fetching returned empty results')
except Exception as e:
    print(f'✗ Data fetching failed: {e}')
"

echo "=== Health Check Complete ==="
```

### Performance Benchmark

```bash
#!/bin/bash
# benchmark.sh - Performance benchmarking

echo "=== Performance Benchmark ==="

# Benchmark training mode
echo "Benchmarking training mode..."
time_start=$(date +%s)
RL_PORTFOLIO_DATA_SPLIT_RATIO=0.5 python main.py --mode=training > /dev/null 2>&1
time_end=$(date +%s)
training_time=$((time_end - time_start))
echo "Training mode: ${training_time} seconds"

# Benchmark evaluation mode
echo "Benchmarking evaluation mode..."
time_start=$(date +%s)
python main.py --mode=evaluation > /dev/null 2>&1
time_end=$(date +%s)
evaluation_time=$((time_end - time_start))
echo "Evaluation mode: ${evaluation_time} seconds"

# Memory usage
echo "Checking memory usage..."
python -c "
import psutil
import os
process = psutil.Process(os.getpid())
memory_mb = process.memory_info().rss / 1024 / 1024
print(f'Current memory usage: {memory_mb:.1f} MB')
"

echo "=== Benchmark Complete ==="
```

This troubleshooting guide covers the most common issues users encounter when working with the different execution modes. For additional help, check the system logs and use the diagnostic commands provided.