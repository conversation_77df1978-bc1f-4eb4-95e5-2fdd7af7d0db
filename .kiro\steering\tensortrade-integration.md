---
inclusion: always
---

# TensorTrade Integration Guidelines

## Core Integration Rules

### Authentic Components Only
- **Never create mock implementations** - always use genuine TensorTrade classes
- **Fix root causes** - don't work around TensorTrade issues with fallback mechanisms
- **Use proper inheritance** - extend TensorTrade base classes correctly

### Required Object Attributes
- **All instruments MUST have `id` attribute** set to their symbol string
- **Use TensorTrade Portfolio class** - never implement custom portfolio tracking
- **Proper component registration** - register ActionScheme and RewardScheme with descriptive names

## Implementation Patterns

### Instrument Setup
```python
# Required pattern for all instruments
instrument = Instrument(symbol, precision=2)
instrument.id = symbol  # Critical: must set id attribute
```

### Portfolio Integration
- Use `tensortrade.oms.Portfolio` class exclusively
- Implement proper wallet and exchange setup
- Ensure portfolio value calculations reflect actual trades
- Validate initial cash ($100,000) is properly tracked

### Trading Mechanics
- **Transaction costs**: Apply 0.1% per trade (from CONFIG.TRADING.TRANSACTION_COST)
- **Slippage modeling**: Implement 0.0% to 1.0% range
- **Actual execution**: Rebalancing actions must change portfolio composition
- **Trade validation**: All transactions must be logged and verifiable

### Component Registration
```python
# Required pattern for custom components
from tensortrade.env.default import create
env = create(
    portfolio=portfolio,
    action_scheme=action_scheme,  # Must be registered
    reward_scheme=reward_scheme,  # Must be registered
    # ... other components
)
```

## Data Pipeline Requirements

### Daily Data Fetching
- ETF symbols: `['VT', 'IEF', 'REET', 'GLD', 'COM']`
- Additional context: ^TNX (10-Year Treasury) daily data
- Use 3-year sliding windows with daily frequency (not monthly aggregated)

### Stream Creation
- Create TensorTrade streams for all price and indicator data
- Ensure proper stream naming and data alignment
- Validate stream continuity and missing data handling
- **Observation window**: Configurable via `CONFIG.TENSORTRADE.window_size` (currently 5 days, adjustable for testing)

## Error Handling Standards

### Root Cause Resolution
- **Never catch and ignore TensorTrade errors** - investigate and fix underlying issues
- **Provide actionable error messages** with full context and suggested fixes
- **Validate component compatibility** before environment creation

### Common Integration Issues
- Missing `id` attribute on instruments → Set `instrument.id = symbol`
- Portfolio value not updating → Verify trade execution in TensorTrade environment
- Component registration failures → Check ActionScheme/RewardScheme naming
- Stream data misalignment → Validate data preprocessing and stream creation

## Validation Checklist

Before running any TensorTrade environment:
- [ ] All instruments have `id` attribute set
- [ ] Portfolio uses TensorTrade Portfolio class
- [ ] ActionScheme and RewardScheme are properly registered
- [ ] Transaction costs and slippage are configured
- [ ] Data streams are properly aligned and named
- [ ] Initial cash amount matches configuration
- [ ] Observation window size is properly configured (variable for testing purposes)