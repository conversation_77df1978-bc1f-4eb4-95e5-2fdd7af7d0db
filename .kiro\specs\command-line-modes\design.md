# Design Document

## Overview

The command-line modes feature adds argument parsing capabilities to the RL portfolio rebalancing system, enabling users to run specific phases (training, evaluation, or full) based on command-line arguments. This design implements a modular execution flow that maintains backward compatibility while providing granular control over system execution phases.

## Architecture

### Command-Line Interface Design

The system will use Python's `argparse` module to handle command-line arguments with the following structure:

```
python main.py [--mode {training,evaluation,full}] [--model-path MODEL_PATH] [--help]
```

**Arguments:**
- `--mode`: Execution mode (default: 'full')
  - `training`: Execute only training phase
  - `evaluation`: Execute only evaluation/backtest phase  
  - `full`: Execute both phases (backward compatibility)
- `--model-path`: Path to trained model file (optional, auto-detected if not provided)
- `--help`: Display usage information

### Execution Flow Architecture

The system will implement a mode-based execution dispatcher that routes to appropriate execution paths:

```
main() → parse_arguments() → validate_mode() → execute_mode()
                                              ├── execute_training_mode()
                                              ├── execute_evaluation_mode()
                                              └── execute_full_mode()
```

### Data Separation Strategy

**Training Mode Data:**
- Uses the first 80% of the 4-year sliding window for training
- Excludes evaluation data to prevent data leakage
- Maintains temporal ordering

**Evaluation Mode Data:**
- Uses the last 20% of the 4-year sliding window for evaluation
- Ensures no overlap with training data
- Provides sufficient data for meaningful backtesting

## Components and Interfaces

### 1. ArgumentParser Component

**Purpose:** Handle command-line argument parsing and validation

**Interface:**
```python
class ArgumentParser:
    def parse_arguments(self, args: List[str] = None) -> argparse.Namespace
    def validate_arguments(self, args: argparse.Namespace) -> bool
    def display_help(self) -> None
```

**Responsibilities:**
- Parse command-line arguments using argparse
- Validate argument combinations and values
- Provide help text and usage examples
- Handle argument parsing errors gracefully

### 2. ModeValidator Component

**Purpose:** Validate execution mode requirements and dependencies

**Interface:**
```python
class ModeValidator:
    def validate_training_mode(self) -> ValidationResult
    def validate_evaluation_mode(self, model_path: str = None) -> ValidationResult
    def validate_full_mode(self) -> ValidationResult
    def check_model_availability(self, model_path: str = None) -> str
```

**Responsibilities:**
- Check data availability for each mode
- Validate trained model existence for evaluation mode
- Verify configuration compatibility
- Return detailed validation results

### 3. ModeExecutor Component

**Purpose:** Execute specific modes with appropriate data and output handling

**Interface:**
```python
class ModeExecutor:
    def execute_training_mode(self, config: Dict) -> TrainingResult
    def execute_evaluation_mode(self, config: Dict, model_path: str) -> EvaluationResult
    def execute_full_mode(self, config: Dict) -> FullResult
```

**Responsibilities:**
- Route execution to appropriate mode handlers
- Manage data splitting and preparation
- Control output generation based on mode
- Handle mode-specific error scenarios

### 4. DataSplitter Component

**Purpose:** Split data appropriately for training and evaluation modes

**Interface:**
```python
class DataSplitter:
    def split_training_evaluation(self, data: pd.DataFrame, split_ratio: float = 0.8) -> Tuple[pd.DataFrame, pd.DataFrame]
    def get_training_data(self, data: pd.DataFrame) -> pd.DataFrame
    def get_evaluation_data(self, data: pd.DataFrame) -> pd.DataFrame
    def validate_data_separation(self, training_data: pd.DataFrame, evaluation_data: pd.DataFrame) -> bool
```

**Responsibilities:**
- Split data temporally for training/evaluation
- Ensure no data leakage between splits
- Validate data separation integrity
- Maintain temporal ordering

### 5. OutputManager Component

**Purpose:** Control output generation based on execution mode

**Interface:**
```python
class OutputManager:
    def configure_for_mode(self, mode: str) -> None
    def should_generate_training_outputs(self) -> bool
    def should_generate_evaluation_outputs(self) -> bool
    def get_output_directory(self, mode: str) -> str
```

**Responsibilities:**
- Configure output generation based on mode
- Manage file naming and directory structure
- Control CSV generation for evaluation mode
- Handle mode-specific logging requirements

## Data Models

### ExecutionMode Enum
```python
from enum import Enum

class ExecutionMode(Enum):
    TRAINING = "training"
    EVALUATION = "evaluation"
    FULL = "full"
```

### ValidationResult DataClass
```python
@dataclass
class ValidationResult:
    is_valid: bool
    error_message: str = ""
    warnings: List[str] = field(default_factory=list)
    required_files: List[str] = field(default_factory=list)
```

### ModeConfig DataClass
```python
@dataclass
class ModeConfig:
    mode: ExecutionMode
    model_path: Optional[str] = None
    data_split_ratio: float = 0.8
    output_directory: str = "results"
    generate_csv: bool = True
    generate_reports: bool = True
```

### ExecutionResult DataClass
```python
@dataclass
class ExecutionResult:
    mode: ExecutionMode
    success: bool
    execution_time: float
    output_files: List[str]
    error_message: str = ""
    metrics: Optional[Dict[str, Any]] = None
```

## Error Handling

### Argument Parsing Errors
- Invalid mode values → Display valid options and exit
- Missing required arguments → Show usage help
- Conflicting arguments → Explain conflicts and suggest corrections

### Mode Validation Errors
- Missing trained model for evaluation → Clear error with model location guidance
- Insufficient data → Specify data requirements and current availability
- Configuration conflicts → Detail specific configuration issues

### Execution Errors
- Training failures → Preserve partial results and provide recovery guidance
- Evaluation failures → Validate model compatibility and data integrity
- Resource constraints → Suggest system requirements and optimization options

### Error Recovery Strategies
- Graceful degradation for non-critical failures
- Automatic fallback to alternative execution paths where appropriate
- Comprehensive error logging with actionable recommendations

## Testing Strategy

### Unit Testing
- **ArgumentParser:** Test all argument combinations and edge cases
- **ModeValidator:** Test validation logic for each mode and error conditions
- **DataSplitter:** Verify data separation integrity and temporal ordering
- **OutputManager:** Validate output generation control and file management

### Integration Testing
- **Mode Execution:** Test complete execution flow for each mode
- **Data Flow:** Verify data consistency across mode boundaries
- **Output Generation:** Validate correct outputs for each mode
- **Error Handling:** Test error scenarios and recovery mechanisms

### End-to-End Testing
- **Training Mode:** Complete training execution with model saving
- **Evaluation Mode:** Full evaluation with existing model and CSV generation
- **Full Mode:** Backward compatibility with existing workflows
- **Mode Transitions:** Test switching between modes with same data

### Performance Testing
- **Memory Usage:** Verify memory efficiency for each mode
- **Execution Time:** Benchmark mode-specific performance
- **Resource Utilization:** Monitor system resource usage patterns
- **Scalability:** Test with various data sizes and configurations

## Implementation Considerations

### Backward Compatibility
- Default behavior (no arguments) maintains current functionality
- Existing configuration files remain compatible
- Output file formats and locations unchanged for full mode
- Logging behavior preserved for full mode execution

### Configuration Integration
- Mode-specific configurations extend existing CONFIG structure
- Environment variables support for automated deployments
- Configuration validation for mode-specific requirements
- Dynamic configuration adjustment based on selected mode

### Model Management
- Automatic model discovery in models directory
- Model metadata validation for compatibility
- Version tracking for model files
- Graceful handling of missing or corrupted models

### Data Management
- Efficient data loading for mode-specific requirements
- Memory optimization for large datasets
- Caching strategies for repeated evaluations
- Data integrity validation across mode boundaries

### Logging and Monitoring
- Mode-specific log levels and formats
- Execution progress tracking for each mode
- Performance metrics collection
- Error tracking and reporting enhancements