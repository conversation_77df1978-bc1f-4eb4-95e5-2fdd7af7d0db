---
inclusion: always
---

# RL Portfolio Rebalancing System

PPO-based reinforcement learning system for optimizing portfolio allocations across 7 ETFs using TensorTrade and stable-baselines3.

## Critical Constraints

### Fixed ETF Universe 
- **Exactly 5 ETFs**: `['VT', 'IEF', 'REET', 'GLD', 'COM']`
- **Additional data**: ^TNX (10-Year Treasury) daily data for training context
- Always validate these exact symbols in ETF-related code

### Portfolio Parameters
- Initial capital: $100,000 (from config)
- Weight constraints: Sum to 1.0, individual weights [0, 1]
- Monthly rebalancing frequency only
- Continuous allocation weights (not discrete buy/sell actions)

## Architecture Patterns

### Configuration-Driven Design
- All parameters in `config.py` - never hardcode values
- Import: `from config import CONFIG`
- Reference: `CONFIG.TRADING.INITIAL_CASH`, `CONFIG.DATA.ETF_SYMBOLS`

### Command-Line Execution Modes
- **Training Mode**: `python main.py --mode=training` - Execute only training phase, skip evaluation
- **Evaluation Mode**: `python main.py --mode=evaluation` - Execute only backtesting with existing model
- **Full Mode**: `python main.py` or `python main.py --mode=full` - Execute both phases (default)
- **Data Separation**: Training uses first 80% of data window, evaluation uses last 20%
- **Mode-Specific Outputs**: Evaluation mode generates CSV files, training mode saves models only

### TensorTrade Integration
- Use authentic TensorTrade components - no mock implementations
- All instruments must have `id` attribute set to symbol
- Use TensorTrade's Portfolio class, not custom implementations
- Register ActionScheme and RewardScheme components with proper names
- Implement real trading mechanics with transaction costs and slippage

### Data Pipeline
- **Daily data fetching**: Yahoo Finance via `yfinance` for daily ETF and ^TNX data
- 3-year training windows using daily data, shifting forward (not expanding)
- **Data splitting**: 80% for training, 20% for evaluation (temporal split, no overlap)
- **Monthly execution**: Program runs at end of each month to output portfolio composition
- Monthly rebalancing frequency for decision implementation
- Technical indicators via `ta` library: RSI, MACD, Bollinger Bands, moving averages

## Code Style

### File Naming Convention
- Logs: `rl_portfolio_rebalancing_YYYYMMDD_HHMMSS.log`
- Models: Include training date and performance metadata
- Results: JSON summaries and CSV histories with `YYYYMMDD_HHMMSS` timestamps

### Error Handling
- Fix root causes - don't catch and ignore errors
- Provide actionable error messages for debugging
- Validate component integration before execution

### Trading Implementation
- Transaction costs: 0.1% per trade (from config)
- Slippage modeling: 0.0% to 1.0% range
- Portfolio tracking must reflect actual executed trades
- Rebalancing actions must change portfolio composition

## Performance Standards

### Evaluation Metrics
- Primary: Sharpe ratio and differential Sharpe ratio
- Baseline: Equal-weight portfolio comparison
- Validation: Out-of-sample testing on unseen data
- Audit trail: Complete logging of trades and decisions

### Testing Requirements
- Verify instrument `id` attribute resolution
- Confirm TensorTrade Portfolio class usage
- Validate trade execution and portfolio value updates
- Test $100,000 initial cash management throughout system