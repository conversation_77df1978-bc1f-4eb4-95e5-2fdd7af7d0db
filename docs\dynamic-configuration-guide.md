# Dynamic Configuration Guide

## Overview

The RL Portfolio Rebalancing System now supports dynamic configuration of `min_data_points` and `checkpoint_frequency` based on the data period. This allows the system to automatically adjust these parameters when the data fetching period is changed from the default 4 years to 2 or 5 years.

## Key Features

### Dynamic Parameters

1. **min_data_points**: Automatically calculated based on expected data size
2. **checkpoint_frequency**: Automatically calculated based on training data size
3. **Data period flexibility**: Support for 2, 4, or 5 years of historical data
4. **Trading vs Calendar days**: Choose between trading days (252/year) or calendar days (365/year)

### Configuration Structure

```python
# Dynamic Data Size Configuration
DATA_SIZE_CONFIG = {
    "expected_years": 4,  # Can be changed to 2 or 5
    "trading_days_per_year": 252,
    "calendar_days_per_year": 365,
    "use_trading_days": True,  # True for trading days, False for calendar days
}

# Dynamic Calculation Configuration
DYNAMIC_CONFIG = {
    "min_data_points": {
        "training_ratio": 0.15,  # Minimum 15% of expected training data
        "evaluation_ratio": 0.25,  # Minimum 25% of expected evaluation data
        "absolute_minimum_training": 200,
        "absolute_minimum_evaluation": 100,
    },
    "checkpoint_frequency": {
        "training_data_ratio": 0.12,  # Checkpoint every 12% of training data
        "minimum_frequency": 50,
        "maximum_frequency": 1000,
    }
}
```

## Usage Examples

### Programmatic Configuration

```python
from config import (
    ConfigManager, 
    set_data_period_years, 
    set_use_trading_days,
    print_dynamic_config_summary
)

# Change data period to 2 years
set_data_period_years(2)

# Use calendar days instead of trading days
set_use_trading_days(False)

# Create config manager and see calculated values
config_manager = ConfigManager("training")
print(f"Min data points: {config_manager.get_min_data_points()}")
print(f"Checkpoint frequency: {config_manager.get_checkpoint_frequency()}")

# Print detailed summary
print_dynamic_config_summary("training")
```

### Environment Variable Configuration

Set environment variables to override configuration:

```bash
# Windows PowerShell
$env:RL_PORTFOLIO_DATA_YEARS="2"
$env:RL_PORTFOLIO_USE_TRADING_DAYS="false"
python main.py --mode=training

# Linux/Mac
export RL_PORTFOLIO_DATA_YEARS=2
export RL_PORTFOLIO_USE_TRADING_DAYS=false
python main.py --mode=training
```

### Configuration Examples by Data Period

#### 2 Years of Data (Trading Days)
- Total data points: ~504
- Training data points: ~403 (80%)
- Evaluation data points: ~101 (20%)
- Min data points (training): 200 (max of 15% × 403 = 60 and absolute minimum 200)
- Min data points (evaluation): 100 (max of 25% × 101 = 25 and absolute minimum 100)
- Checkpoint frequency: 50 (max of 12% × 403 = 48 and minimum 50)

#### 4 Years of Data (Trading Days) - Default
- Total data points: ~1,008
- Training data points: ~806 (80%)
- Evaluation data points: ~202 (20%)
- Min data points (training): 200 (max of 15% × 806 = 121 and absolute minimum 200)
- Min data points (evaluation): 100 (max of 25% × 202 = 50 and absolute minimum 100)
- Checkpoint frequency: 96 (12% × 806 = 96)

#### 5 Years of Data (Trading Days)
- Total data points: ~1,260
- Training data points: ~1,008 (80%)
- Evaluation data points: ~252 (20%)
- Min data points (training): 200 (max of 15% × 1,008 = 151 and absolute minimum 200)
- Min data points (evaluation): 100 (max of 25% × 252 = 63 and absolute minimum 100)
- Checkpoint frequency: 120 (12% × 1,008 = 120)

## Environment Variables

| Variable | Description | Valid Values | Default |
|----------|-------------|--------------|---------|
| `RL_PORTFOLIO_DATA_YEARS` | Override expected years of data | 2, 4, 5 (or any positive integer) | 4 |
| `RL_PORTFOLIO_USE_TRADING_DAYS` | Use trading days vs calendar days | true, false | true |

## Backward Compatibility

The system maintains full backward compatibility:

1. **Static values**: If `min_data_points` or `checkpoint_frequency` are set to integer values in the configuration, those static values are used instead of dynamic calculation.

2. **Legacy configuration**: Existing configurations continue to work without modification.

3. **Gradual adoption**: You can enable dynamic calculation by setting values to `"dynamic"` in the configuration.

## Testing Dynamic Configuration

Use the provided test script to verify dynamic configuration:

```bash
python test_dynamic_config.py
```

This script demonstrates:
- Dynamic calculation with different data periods (2, 4, 5 years)
- Trading days vs calendar days comparison
- Environment variable overrides
- Mode-specific calculations

## Integration with Main System

The dynamic configuration integrates seamlessly with the existing command-line modes:

```bash
# Training with 2 years of data
export RL_PORTFOLIO_DATA_YEARS=2
python main.py --mode=training

# Evaluation with 5 years of data using calendar days
export RL_PORTFOLIO_DATA_YEARS=5
export RL_PORTFOLIO_USE_TRADING_DAYS=false
python main.py --mode=evaluation

# Full mode with default 4 years
python main.py --mode=full
```

## Benefits

1. **Automatic scaling**: Parameters scale appropriately with data size
2. **Flexibility**: Easy to test different data periods
3. **Consistency**: Maintains proportional relationships across different data sizes
4. **Environment-friendly**: Can be configured via environment variables for deployment
5. **Backward compatible**: Existing configurations continue to work

## Troubleshooting

### Common Issues

1. **Invalid data years**: Ensure `RL_PORTFOLIO_DATA_YEARS` is a positive integer
2. **Invalid trading days setting**: Ensure `RL_PORTFOLIO_USE_TRADING_DAYS` is "true" or "false"
3. **Unexpected values**: Use `print_dynamic_config_summary()` to debug calculated values

### Debug Information

```python
from config import ConfigManager

config_manager = ConfigManager("training")
summary = config_manager.get_dynamic_config_summary()
print(summary)
```

This will show all calculated values and help identify any configuration issues.