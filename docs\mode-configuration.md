# Mode-Specific Configuration System

This document describes the mode-specific configuration system for the RL Portfolio Rebalancing System.

## Overview

The configuration system supports three execution modes with different settings:

- **Training Mode**: Execute only training phase, minimal output generation
- **Evaluation Mode**: Execute only evaluation phase, full output generation  
- **Full Mode**: Execute both phases, backward compatible with existing workflows

## Configuration Structure

### Mode-Specific Settings

Each mode has the following configuration sections:

#### Data Split Ratio
- Controls the temporal split between training and evaluation data
- Default: 0.8 (80% training, 20% evaluation)
- Can be overridden via `RL_PORTFOLIO_DATA_SPLIT_RATIO` environment variable

#### Output Settings
- `generate_csv`: Whether to generate CSV files
- `generate_json`: Whether to generate JSON performance summaries
- `generate_reports`: Whether to generate text reports
- `file_prefix`: Prefix for output files
- `timestamp_format`: Format for timestamps in filenames

#### Validation Requirements
- `require_data`: Whether data is required for this mode
- `require_model`: Whether a trained model is required
- `min_data_points`: Minimum number of data points required

#### Execution Settings
- `skip_evaluation`: Whether to skip evaluation phase
- `skip_training`: Whether to skip training phase
- `save_model`: Whether to save trained models
- `enable_tensorboard`: Whether to enable TensorBoard logging
- `checkpoint_frequency`: How often to save checkpoints during training

## Usage Examples

### Basic Usage

```python
from config import ConfigManager

# Create configuration for training mode
config = ConfigManager("training")

# Check mode-specific settings
if config.should_generate_csv:
    print("CSV generation enabled")

if config.should_skip_evaluation:
    print("Evaluation will be skipped")

# Get data split ratio
ratio = config.data_split_ratio
print(f"Using {ratio:.1%} of data for training")
```

### Environment Variable Overrides

```bash
# Override execution mode
export RL_PORTFOLIO_MODE=evaluation

# Override data split ratio
export RL_PORTFOLIO_DATA_SPLIT_RATIO=0.8

# Override model path for evaluation
export RL_PORTFOLIO_MODEL_PATH=/path/to/model.zip

# Override output directory
export RL_PORTFOLIO_OUTPUT_DIR=/custom/output/path
```

### Legacy Compatibility

```python
from config import CONFIG

# Legacy access patterns still work
initial_cash = CONFIG.TRADING.initial_cash
etf_symbols = CONFIG.DATA.etf_symbols

# Get mode-specific configuration
mode_config = CONFIG.get_mode_config("training")
```

### Validation

```python
from config import ConfigManager

config = ConfigManager("evaluation")

# Validate mode requirements
is_valid, errors = config.validate_mode_requirements()
if not is_valid:
    for error in errors:
        print(f"Validation error: {error}")
```

## Mode Configurations

### Training Mode
- **Purpose**: Train models without evaluation
- **Data**: Uses first 80% of data window
- **Outputs**: Saves trained models only
- **Settings**:
  - `generate_csv`: False
  - `skip_evaluation`: True
  - `save_model`: True
  - `enable_tensorboard`: True

### Evaluation Mode
- **Purpose**: Evaluate existing models without training
- **Data**: Uses last 20% of data window
- **Outputs**: Generates CSV files, JSON summaries, and reports
- **Settings**:
  - `generate_csv`: True
  - `skip_training`: True
  - `require_model`: True
  - `enable_tensorboard`: False

### Full Mode
- **Purpose**: Complete training and evaluation workflow
- **Data**: Uses full data window with temporal split
- **Outputs**: Generates all outputs from both phases
- **Settings**:
  - `generate_csv`: True
  - `skip_evaluation`: False
  - `skip_training`: False
  - `save_model`: True

## Environment Variables

| Variable | Description | Valid Values |
|----------|-------------|--------------|
| `RL_PORTFOLIO_MODE` | Override execution mode | training, evaluation, full |
| `RL_PORTFOLIO_MODEL_PATH` | Custom model path for evaluation | File path |
| `RL_PORTFOLIO_DATA_SPLIT_RATIO` | Override data split ratio | 0.0-1.0 |
| `RL_PORTFOLIO_OUTPUT_DIR` | Override output directory | Directory path |
| `RL_PORTFOLIO_SKIP_VALIDATION` | Skip validation (testing only) | true, false |

## Integration with Existing Code

The configuration system is designed to be backward compatible. Existing code using the old configuration patterns will continue to work:

```python
# Old pattern (still works)
from config import TRADING_CONFIG
initial_cash = TRADING_CONFIG["initial_cash"]

# New pattern (recommended)
from config import ConfigManager
config = ConfigManager("training")
initial_cash = config.get_all_config()["trading"]["initial_cash"]
```

## Testing

The configuration system includes comprehensive tests:

```bash
# Run configuration tests
python test_mode_config.py

# Test specific functionality
python -c "from config import ConfigManager; print(ConfigManager('training').should_generate_csv)"
```

## Error Handling

The system provides detailed validation and error messages:

- Invalid configuration values are automatically corrected with warnings
- Missing required files are detected before execution
- Environment variable validation prevents invalid settings
- Mode requirements are validated before starting execution

## Performance Characteristics

### Training Mode Performance
- **Execution Time**: 15-30 minutes (depending on system specifications)
- **Memory Usage**: 2-4GB peak during model training
- **CPU Usage**: High during training phase, moderate during data processing
- **Disk Usage**: ~50-100MB for model files, ~10-50MB for logs
- **Network Usage**: Moderate during initial data fetching, minimal afterward

### Evaluation Mode Performance
- **Execution Time**: 2-5 minutes for standard evaluation
- **Memory Usage**: 1-2GB peak during backtesting
- **CPU Usage**: Moderate throughout execution
- **Disk Usage**: ~20-50MB for CSV files and reports
- **Network Usage**: Minimal (uses cached data when available)

### Full Mode Performance
- **Execution Time**: 20-35 minutes (combined training + evaluation)
- **Memory Usage**: 2-4GB peak (same as training mode)
- **CPU Usage**: High during training, moderate during evaluation
- **Disk Usage**: ~70-150MB for all outputs combined
- **Network Usage**: Moderate during data fetching phase

## Advanced Configuration Options

### Custom Data Split Ratios by Mode

```python
# Different split ratios for different use cases
MODE_SPECIFIC_SPLITS = {
    "training": 0.8,      # Standard 80/20 split
    "evaluation": 0.8,    # Standard 80/20 split for consistency
    "full": 0.8,          # Standard 80/20 split for production models
}

# Apply via environment variable
export RL_PORTFOLIO_DATA_SPLIT_RATIO=0.85
```

### Output File Customization

```python
# Custom output settings per mode
CUSTOM_OUTPUT_SETTINGS = {
    "training": {
        "file_prefix": "train_",
        "timestamp_format": "%Y%m%d_%H%M%S",
        "generate_csv": False,
        "generate_json": False,
        "generate_reports": False
    },
    "evaluation": {
        "file_prefix": "eval_",
        "timestamp_format": "%Y%m%d_%H%M%S",
        "generate_csv": True,
        "generate_json": True,
        "generate_reports": True
    },
    "full": {
        "file_prefix": "",
        "timestamp_format": "%Y%m%d_%H%M%S",
        "generate_csv": True,
        "generate_json": True,
        "generate_reports": True
    }
}
```

### Mode-Specific Validation Rules

```python
# Validation requirements by mode
VALIDATION_RULES = {
    "training": {
        "require_data": True,
        "require_model": False,
        "min_data_points": 1000,
        "max_memory_usage_gb": 8,
        "min_disk_space_gb": 1
    },
    "evaluation": {
        "require_data": True,
        "require_model": True,
        "min_data_points": 200,
        "max_memory_usage_gb": 4,
        "min_disk_space_gb": 0.5
    },
    "full": {
        "require_data": True,
        "require_model": False,
        "min_data_points": 1000,
        "max_memory_usage_gb": 8,
        "min_disk_space_gb": 1.5
    }
}
```

## Migration Guide for Existing Users

### From Legacy Configuration (Pre-Mode System)

**Old Pattern:**
```python
# Legacy configuration access
from config import TRADING_CONFIG, DATA_CONFIG
initial_cash = TRADING_CONFIG["initial_cash"]
etf_symbols = DATA_CONFIG["etf_symbols"]

# Direct execution (no mode selection)
python main.py
```

**New Pattern:**
```python
# Mode-aware configuration access
from config import ConfigManager
config = ConfigManager("full")  # Maintains backward compatibility
initial_cash = config.get_all_config()["trading"]["initial_cash"]
etf_symbols = config.get_all_config()["data"]["etf_symbols"]

# Explicit mode selection
python main.py --mode=full
```

### Migration Steps

1. **Update Scripts:**
   ```bash
   # Old script
   #!/bin/bash
   python main.py > output.log 2>&1

   # New script (backward compatible)
   #!/bin/bash
   python main.py --mode=full > output.log 2>&1
   ```

2. **Update Configuration Access:**
   ```python
   # Replace direct config imports with ConfigManager
   # Old
   from config import TRAINING_CONFIG
   timesteps = TRAINING_CONFIG["total_timesteps"]

   # New
   from config import ConfigManager
   config = ConfigManager("training")
   timesteps = config.get_all_config()["training"]["total_timesteps"]
   ```

3. **Update Environment Setup:**
   ```bash
   # Add mode-specific environment variables
   export RL_PORTFOLIO_MODE=full
   export RL_PORTFOLIO_UNICODE_MODE=auto
   ```

### Compatibility Matrix

| Feature | Legacy Support | Mode System | Notes |
|---------|---------------|-------------|-------|
| Direct execution | ✅ | ✅ | `python main.py` defaults to full mode |
| Config imports | ✅ | ✅ | Old imports still work |
| Output files | ✅ | ✅ | Same file formats and locations |
| Environment vars | ❌ | ✅ | New environment variables available |
| Mode selection | ❌ | ✅ | New feature for granular control |
| Performance optimization | ❌ | ✅ | Mode-specific optimizations |

## Best Practices

1. **Use ConfigManager**: Prefer the new ConfigManager class over direct config access
2. **Environment Variables**: Use environment variables for deployment-specific overrides
3. **Validation**: Always validate mode requirements before execution
4. **Testing**: Use silent mode during testing to suppress warning messages
5. **Documentation**: Keep configuration changes documented and tested
6. **Mode Selection**: Choose appropriate mode for your use case:
   - Use `training` for model development and experimentation
   - Use `evaluation` for backtesting and performance analysis
   - Use `full` for production workflows and backward compatibility
7. **Resource Management**: Monitor system resources and adjust configurations accordingly
8. **Error Handling**: Implement proper error handling for mode validation failures
9. **Logging**: Configure appropriate logging levels for different modes
10. **Automation**: Use environment variables for automated deployments and CI/CD pipelines