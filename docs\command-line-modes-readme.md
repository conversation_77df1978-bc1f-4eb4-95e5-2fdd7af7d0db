# Command-Line Modes - Complete Documentation

This document serves as the comprehensive guide for the command-line modes feature of the RL Portfolio Rebalancing System.

## Quick Start

### Basic Usage

```bash
# Training only (faster development)
python main.py --mode=training

# Evaluation only (backtesting existing models)
python main.py --mode=evaluation

# Full workflow (default, backward compatible)
python main.py --mode=full
python main.py  # Same as above
```

### Help and Information

```bash
# Display help
python main.py --help

# Check system status
python -c "from config import ConfigManager; print(ConfigManager('training'))"
```

## Feature Overview

### Three Execution Modes

1. **Training Mode** (`--mode=training`)
   - Executes only the training phase
   - Uses first 80% of data for training
   - Saves trained model to `models/` directory
   - Skips CSV generation and performance reporting
   - Optimal for model development and experimentation

2. **Evaluation Mode** (`--mode=evaluation`)
   - Executes only the evaluation/backtesting phase
   - Uses last 20% of data for evaluation
   - Requires existing trained model
   - Generates comprehensive CSV reports and analysis
   - Optimal for performance analysis and model validation

3. **Full Mode** (`--mode=full` or default)
   - Executes both training and evaluation phases
   - Maintains complete backward compatibility
   - Generates all outputs from both phases
   - Optimal for production workflows

### Key Benefits

- **⚡ Faster Development**: Train and evaluate separately for quicker iteration
- **💾 Resource Efficiency**: Use only the resources needed for specific tasks
- **🔧 Better Automation**: Integrate specific phases into CI/CD pipelines
- **🐛 Improved Debugging**: Isolate issues to specific execution phases
- **📊 Flexible Deployment**: Deploy training and evaluation on different schedules
- **🔄 Backward Compatible**: Existing workflows continue to work unchanged

## Documentation Structure

### Core Documentation

| Document | Purpose | Audience |
|----------|---------|----------|
| **[Usage Examples](usage-examples.md)** | Comprehensive usage patterns and integration examples | All users |
| **[Mode Configuration](mode-configuration.md)** | Detailed configuration options and settings | Advanced users |
| **[Troubleshooting](mode-troubleshooting.md)** | Common issues and solutions | All users |
| **[Migration Guide](migration-guide.md)** | Step-by-step upgrade instructions | Existing users |
| **[Performance Benchmarks](performance-benchmarks.md)** | Detailed performance analysis and optimization | System administrators |

### Specialized Documentation

| Document | Purpose | Audience |
|----------|---------|----------|
| **[Unicode Logging](unicode-logging-troubleshooting.md)** | Unicode and encoding issues | International users |
| **[Error Handling](error-handling-implementation.md)** | Error handling and recovery | Developers |
| **[System Requirements](system-requirements.md)** | Hardware and software requirements | System administrators |

## Quick Reference

### Command-Line Arguments

| Argument | Values | Default | Description |
|----------|--------|---------|-------------|
| `--mode` | `training`, `evaluation`, `full` | `full` | Execution mode selection |
| `--model-path` | File path | Auto-detect | Custom model path for evaluation |
| `--help` | - | - | Display help information |

### Environment Variables

| Variable | Values | Default | Description |
|----------|--------|---------|-------------|
| `RL_PORTFOLIO_MODE` | `training`, `evaluation`, `full` | `full` | Override execution mode |
| `RL_PORTFOLIO_MODEL_PATH` | File path | Auto-detect | Custom model path |
| `RL_PORTFOLIO_DATA_SPLIT_RATIO` | 0.0-1.0 | 0.8 | Training/evaluation data split |
| `RL_PORTFOLIO_OUTPUT_DIR` | Directory path | `results` | Output directory |
| `RL_PORTFOLIO_UNICODE_MODE` | `auto`, `unicode`, `ascii` | `auto` | Unicode logging mode |

### Exit Codes

| Code | Meaning | Description |
|------|---------|-------------|
| 0 | Success | Execution completed successfully |
| 1 | General error | Unspecified error occurred |
| 2 | Invalid arguments | Command-line arguments are invalid |
| 3 | Mode validation failed | Mode requirements not met |
| 4 | Required files not found | Missing data or model files |
| 5 | Import/dependency error | Missing Python packages |
| 6 | Network/data fetching error | Data download failed |
| 7 | Permission error | Insufficient file/directory permissions |

## Performance Summary

### Execution Times (Baseline System)

| Mode | Mean Time | Use Case |
|------|-----------|----------|
| **Training** | 17.5 min | Model development |
| **Evaluation** | 3.2 min | Performance analysis |
| **Full** | 21.2 min | Complete workflow |

### Resource Requirements

| Mode | Peak RAM | Disk Space | CPU Usage |
|------|----------|------------|-----------|
| **Training** | 4.2 GB | 220 MB | 78% avg |
| **Evaluation** | 1.9 GB | 85 MB | 45% avg |
| **Full** | 4.3 GB | 290 MB | 68% avg |

## Common Use Cases

### Development Workflow

```bash
# 1. Develop and train model
python main.py --mode=training

# 2. Test model performance
python main.py --mode=evaluation

# 3. Iterate based on results
python main.py --mode=training  # Retrain with adjustments
```

### Production Deployment

```bash
# Monthly production run
export RL_PORTFOLIO_MODE=full
export RL_PORTFOLIO_OUTPUT_DIR=/var/results/$(date +%Y-%m)
python main.py
```

### CI/CD Integration

```yaml
# GitHub Actions example
- name: Train Model
  run: python main.py --mode=training
  
- name: Validate Model
  run: python main.py --mode=evaluation
  
- name: Archive Results
  uses: actions/upload-artifact@v2
  with:
    name: model-results
    path: results/
```

### Batch Processing

```bash
# Process multiple configurations
for ratio in 0.75 0.8 0.85; do
    export RL_PORTFOLIO_DATA_SPLIT_RATIO=$ratio
    python main.py --mode=training
    python main.py --mode=evaluation
done
```

## Integration Examples

### Python Script Integration

```python
import subprocess
import os

def run_training():
    """Run training mode and return model path."""
    env = os.environ.copy()
    env['RL_PORTFOLIO_MODE'] = 'training'
    
    result = subprocess.run(['python', 'main.py'], env=env)
    return result.returncode == 0

def run_evaluation(model_path=None):
    """Run evaluation mode with optional model path."""
    env = os.environ.copy()
    env['RL_PORTFOLIO_MODE'] = 'evaluation'
    if model_path:
        env['RL_PORTFOLIO_MODEL_PATH'] = model_path
    
    result = subprocess.run(['python', 'main.py'], env=env)
    return result.returncode == 0

# Complete workflow
if run_training():
    print("Training completed successfully")
    if run_evaluation():
        print("Evaluation completed successfully")
    else:
        print("Evaluation failed")
else:
    print("Training failed")
```

### Docker Integration

```dockerfile
FROM python:3.9-slim

WORKDIR /app
COPY requirements.txt .
RUN pip install -r requirements.txt

COPY . .

# Set default environment
ENV RL_PORTFOLIO_MODE=full
ENV RL_PORTFOLIO_UNICODE_MODE=ascii

# Support mode override
CMD ["python", "main.py"]
```

```bash
# Run different modes
docker run -e RL_PORTFOLIO_MODE=training rl-portfolio
docker run -e RL_PORTFOLIO_MODE=evaluation rl-portfolio
```

## Troubleshooting Quick Reference

### Common Issues

1. **"Invalid mode argument"**
   ```bash
   # Fix: Use correct mode name
   python main.py --mode=training  # Not --mode=train
   ```

2. **"No trained model exists"**
   ```bash
   # Fix: Train model first or specify path
   python main.py --mode=training
   python main.py --mode=evaluation --model-path=path/to/model.zip
   ```

3. **"Permission denied"**
   ```bash
   # Fix: Check directory permissions
   chmod 755 results/ models/ logs/
   ```

4. **"Memory error"**
   ```bash
   # Fix: Reduce data split ratio
   RL_PORTFOLIO_DATA_SPLIT_RATIO=0.6 python main.py --mode=training
   ```

### Diagnostic Commands

```bash
# Check system health
python -c "
from config import ConfigManager
config = ConfigManager('training')
is_valid, errors = config.validate_mode_requirements()
print(f'Valid: {is_valid}, Errors: {errors}')
"

# Check available models
ls -la models/

# Check disk space
df -h

# Check memory usage
free -h
```

## Migration from Legacy System

### Backward Compatibility

✅ **No changes required** - existing scripts continue to work:

```bash
# These commands work identically in old and new systems
python main.py
python main.py > output.log
python main.py 2>&1 | tee log.txt
```

### Optional Enhancements

🚀 **New features available when ready**:

```bash
# Explicit mode selection (recommended)
python main.py --mode=full

# Separate training and evaluation
python main.py --mode=training
python main.py --mode=evaluation

# Environment variable configuration
export RL_PORTFOLIO_MODE=evaluation
python main.py
```

### Migration Timeline

1. **Week 1**: Test compatibility with existing workflows
2. **Week 2**: Update scripts to use explicit `--mode=full`
3. **Week 3**: Begin using separate modes for development
4. **Week 4**: Implement advanced features and optimizations

## Support and Resources

### Getting Help

1. **Documentation**: Check the relevant documentation file for your use case
2. **Help Command**: Run `python main.py --help` for quick reference
3. **Diagnostics**: Use the diagnostic commands provided in troubleshooting guides
4. **Configuration**: Validate your configuration with the ConfigManager

### Reporting Issues

When reporting issues, please include:

1. **Command used**: Exact command line that failed
2. **Error message**: Complete error output
3. **System info**: OS, Python version, available RAM
4. **Configuration**: Relevant environment variables
5. **Log files**: Contents of relevant log files

### Contributing

The command-line modes system is designed to be extensible. Key areas for contribution:

1. **New modes**: Additional execution modes for specific use cases
2. **Performance optimization**: Improvements to execution speed and resource usage
3. **Integration examples**: Additional integration patterns and examples
4. **Documentation**: Improvements to documentation and examples

## Version History

### v2.0.0 - Command-Line Modes Release
- ✅ Added training, evaluation, and full execution modes
- ✅ Implemented comprehensive argument parsing and validation
- ✅ Added mode-specific configuration system
- ✅ Created extensive documentation and examples
- ✅ Maintained 100% backward compatibility
- ✅ Added performance benchmarks and optimization guides

### Future Roadmap

- **v2.1.0**: Additional modes (validation, hyperparameter tuning)
- **v2.2.0**: Enhanced parallel processing and cloud integration
- **v2.3.0**: Advanced configuration management and templating
- **v3.0.0**: Web interface and API endpoints

---

This comprehensive documentation provides everything needed to effectively use the command-line modes feature. For specific use cases, refer to the individual documentation files linked throughout this guide.