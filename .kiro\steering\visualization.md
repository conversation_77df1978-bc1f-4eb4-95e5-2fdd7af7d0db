# Visualization System Guidelines

## Core Visualization Components

### Dashboard Generation
- **Evaluation Dashboard**: Comprehensive multi-panel dashboard created in evaluation mode
- **File Output**: `evaluation_dashboard_{timestamp}.png` in results directory
- **Components**: Performance metrics, asset allocation pie chart, returns distribution, portfolio value over time
- **Dynamic Configuration**: Uses `CONFIG.DATA.etf_symbols` for current ETF universe

### Portfolio Visualization
- **Multi-Panel Charts**: Portfolio weights over time, value progression, latest allocation
- **File Output**: `portfolio_visualization_{timestamp}.png` in results directory
- **Statistics**: Detailed weight statistics and performance metrics
- **Time Series**: Complete portfolio evolution visualization

## Implementation Patterns

### Configuration Access
```python
from config import CONFIG
etf_symbols = CONFIG.DATA.etf_symbols  # Always use lowercase attribute names
```

### File Naming Resolution
- **Prefixed Files**: Handle `evaluation_` prefixed files in evaluation mode
- **Fallback**: Support non-prefixed files for backward compatibility
- **Auto-Detection**: Automatically find latest timestamp files

### Dynamic ETF Handling
```python
# Filter weights to only include ETF columns that exist in the data
available_etfs = [symbol for symbol in etf_symbols if symbol in weights_df.columns]
latest_weights = weights_df[available_etfs].iloc[-1]
```

## Error Handling Standards

### Graceful Degradation
- **Missing Files**: Log warnings but continue execution
- **Data Issues**: Provide fallback visualizations with available data
- **Configuration Errors**: Use default values with clear error messages

### Logging Requirements
- **Progress Tracking**: Log each visualization step
- **Error Details**: Include full stack traces for debugging
- **Success Confirmation**: Confirm file creation with paths

## Current ETF Configuration

### Supported Symbols
- **Active ETFs**: `['VT', 'IEF', 'REET', 'GLD', 'COM']` (5 ETFs)
- **Risk-Free Rate**: ^TNX (10-Year Treasury)
- **Data Period**: 3-year sliding windows with daily frequency

### Visualization Adaptability
- **Dynamic Sizing**: Charts adapt to actual number of ETFs
- **Color Schemes**: Consistent color mapping across visualizations
- **Label Management**: Automatic label generation from configuration

## Quality Standards

### Chart Aesthetics
- **Style**: Use `seaborn-v0_8` style for professional appearance
- **Colors**: Consistent color palette across all charts
- **Fonts**: Clear, readable fonts with appropriate sizing
- **Layout**: Well-spaced multi-panel layouts with proper titles

### Data Integrity
- **Validation**: Verify data completeness before visualization
- **Accuracy**: Ensure calculations match performance metrics
- **Consistency**: Maintain consistent data representation across charts

## Integration Requirements

### Mode-Specific Generation
- **Evaluation Mode**: Generate both dashboard and portfolio visualizations
- **Training Mode**: Skip visualization generation (focus on model training)
- **Full Mode**: Generate visualizations after evaluation phase

### Output Management
- **File Organization**: Save all visualizations to results directory
- **Timestamp Consistency**: Use consistent timestamp format across all files
- **Cleanup**: No automatic cleanup - preserve all generated visualizations