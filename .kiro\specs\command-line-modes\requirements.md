# Requirements Document

## Introduction

This feature adds command-line argument support to the RL portfolio rebalancing system to enable separate training and evaluation (backtest) modes. Currently, the system runs both training and evaluation phases in a single execution. The new feature will allow users to run only the training phase or only the evaluation phase based on command-line arguments, with specific data handling and output generation tailored to each mode.

## Requirements

### Requirement 1

**User Story:** As a system operator, I want to run only the training phase of the RL portfolio rebalancing system, so that I can train models without running evaluation and save computational resources.

#### Acceptance Criteria

1. WHEN the user runs `python main.py --mode=training` THEN the system SHALL execute only the training phase
2. WHEN training mode is active THEN the system SHALL skip the backtesting framework execution
3. WHEN training mode is active THEN the system SHALL skip performance metrics calculation
4. WHEN training mode is active THEN the system SHALL skip baseline strategy comparisons
5. WHEN training mode is active THEN the system SHALL save the trained model to the models directory
6. WHEN training mode is active THEN the system SHALL log training progress and completion status
7. WHEN training mode completes successfully THEN the system SHALL exit with status code 0

### Requirement 2

**User Story:** As a system operator, I want to run only the evaluation (backtest) phase of the RL portfolio rebalancing system, so that I can evaluate existing trained models without retraining.

#### Acceptance Criteria

1. WHEN the user runs `python main.py --mode=evaluation` THEN the system SHALL execute only the evaluation phase
2. WHEN evaluation mode is active THEN the system SHALL skip the training phase
3. WHEN evaluation mode is active THEN the system SHALL load an existing trained model from the models directory
4. WHEN evaluation mode is active THEN the system SHALL use only evaluation data for backtesting
5. WHEN evaluation mode is active THEN the system SHALL generate CSV files for portfolio history, returns history, and weights history
6. WHEN evaluation mode is active THEN the system SHALL calculate comprehensive performance metrics
7. WHEN evaluation mode is active THEN the system SHALL run baseline strategy comparisons
8. WHEN evaluation mode is active THEN the system SHALL generate the comprehensive results report

### Requirement 3

**User Story:** As a system operator, I want to run the complete system with both training and evaluation phases, so that I can maintain backward compatibility with existing workflows.

#### Acceptance Criteria

1. WHEN the user runs `python main.py` without mode arguments THEN the system SHALL execute both training and evaluation phases
2. WHEN the user runs `python main.py --mode=full` THEN the system SHALL execute both training and evaluation phases
3. WHEN full mode is active THEN the system SHALL maintain the current behavior and output generation
4. WHEN full mode is active THEN the system SHALL generate all training logs and evaluation outputs

### Requirement 4

**User Story:** As a system operator, I want clear error messages when I provide invalid command-line arguments, so that I can correct my usage quickly.

#### Acceptance Criteria

1. WHEN the user provides an invalid mode argument THEN the system SHALL display a clear error message
2. WHEN the user provides an invalid mode argument THEN the system SHALL show the valid mode options
3. WHEN the user provides an invalid mode argument THEN the system SHALL exit with status code 1
4. WHEN the user runs `python main.py --help` THEN the system SHALL display usage instructions
5. WHEN evaluation mode is requested but no trained model exists THEN the system SHALL display a clear error message and exit

### Requirement 5

**User Story:** As a system operator, I want evaluation mode to use only evaluation-specific data, so that I can ensure proper separation between training and testing data.

#### Acceptance Criteria

1. WHEN evaluation mode is active THEN the system SHALL use only the evaluation portion of the data window
2. WHEN evaluation mode is active THEN the system SHALL not use any training data for backtesting
3. WHEN evaluation mode is active THEN the system SHALL validate data separation before starting evaluation
4. WHEN evaluation mode is active THEN the system SHALL log the evaluation data date range
5. WHEN evaluation mode is active THEN the system SHALL ensure temporal alignment of evaluation data

### Requirement 6

**User Story:** As a system operator, I want evaluation mode to generate only the necessary CSV outputs, so that I can focus on evaluation results without unnecessary file generation.

#### Acceptance Criteria

1. WHEN evaluation mode is active THEN the system SHALL generate portfolio_history_[timestamp].csv
2. WHEN evaluation mode is active THEN the system SHALL generate returns_history_[timestamp].csv  
3. WHEN evaluation mode is active THEN the system SHALL generate weights_history_[timestamp].csv
4. WHEN evaluation mode is active THEN the system SHALL generate performance_summary_[timestamp].json
5. WHEN evaluation mode is active THEN the system SHALL generate comprehensive_results_report_[timestamp].txt
6. WHEN evaluation mode is active THEN the system SHALL NOT generate training-specific log files
7. WHEN evaluation mode is active THEN the system SHALL save all evaluation outputs to the results directory

### Requirement 7

**User Story:** As a system operator, I want the system to validate the execution mode and required dependencies before starting, so that I can catch configuration issues early.

#### Acceptance Criteria

1. WHEN any mode is requested THEN the system SHALL validate the command-line arguments before execution
2. WHEN training mode is requested THEN the system SHALL validate that training data is available
3. WHEN evaluation mode is requested THEN the system SHALL validate that a trained model exists
4. WHEN evaluation mode is requested THEN the system SHALL validate that evaluation data is available
5. WHEN validation fails THEN the system SHALL display specific error messages and exit gracefully
6. WHEN validation succeeds THEN the system SHALL log the validated configuration and proceed