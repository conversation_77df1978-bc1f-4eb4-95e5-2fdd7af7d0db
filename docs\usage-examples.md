# RL Portfolio Rebalancing System - Usage Examples

This document provides comprehensive usage examples for all execution modes of the RL Portfolio Rebalancing System.

## Table of Contents

1. [Basic Usage Examples](#basic-usage-examples)
2. [Advanced Configuration](#advanced-configuration)
3. [Environment Variable Usage](#environment-variable-usage)
4. [Integration Examples](#integration-examples)
5. [Automation and Scripting](#automation-and-scripting)
6. [Performance Optimization](#performance-optimization)

## Basic Usage Examples

### Training Mode

Execute only the training phase to create new models:

```bash
# Basic training mode
python main.py --mode=training

# Training with verbose logging
python main.py --mode=training 2>&1 | tee training_output.log

# Training with custom data split ratio
RL_PORTFOLIO_DATA_SPLIT_RATIO=0.85 python main.py --mode=training
```

**Expected Output:**
- Trained model saved to `models/ppo_portfolio_agent_YYYYMMDD_HHMMSS.zip`
- Training logs in `logs/rl_portfolio_rebalancing_YYYYMMDD_HHMMSS.log`
- TensorBoard logs for monitoring training progress
- No CSV files or performance reports generated

**Use Cases:**
- Initial model training
- Retraining with new data
- Hyperparameter tuning experiments
- Batch training for multiple configurations

### Evaluation Mode

Execute only the evaluation phase with existing models:

```bash
# Basic evaluation with auto-detected model
python main.py --mode=evaluation

# Evaluation with specific model
python main.py --mode=evaluation --model-path=models/ppo_portfolio_agent_20240101_120000.zip

# Evaluation with custom output directory
RL_PORTFOLIO_OUTPUT_DIR=results/custom_eval python main.py --mode=evaluation
```

**Expected Output:**
- `results/portfolio_history_YYYYMMDD_HHMMSS.csv` - Portfolio value over time
- `results/returns_history_YYYYMMDD_HHMMSS.csv` - Daily returns data
- `results/weights_history_YYYYMMDD_HHMMSS.csv` - Asset allocation weights
- `results/performance_summary_YYYYMMDD_HHMMSS.json` - Performance metrics
- `results/comprehensive_results_report_YYYYMMDD_HHMMSS.txt` - Detailed analysis

**Use Cases:**
- Backtesting existing models
- Performance analysis and comparison
- Model validation on new data
- Generating reports for stakeholders

### Full Mode

Execute both training and evaluation phases:

```bash
# Default full mode (backward compatible)
python main.py

# Explicit full mode
python main.py --mode=full

# Full mode with custom settings
RL_PORTFOLIO_DATA_SPLIT_RATIO=0.85 python main.py --mode=full
```

**Expected Output:**
- All outputs from both training and evaluation modes
- Complete workflow from data processing to final reports
- Maintains backward compatibility with existing scripts

**Use Cases:**
- Complete end-to-end execution
- Production workflows
- Comprehensive model development and testing
- Legacy script compatibility

## Advanced Configuration

### Custom Model Management

```bash
# Create models with descriptive names
python main.py --mode=training
# Rename the generated model for clarity
mv models/ppo_portfolio_agent_$(date +%Y%m%d_%H%M%S).zip models/experiment_v1.zip

# Use the custom model for evaluation
python main.py --mode=evaluation --model-path=models/experiment_v1.zip
```

### Data Split Customization

```bash
# Use 80% for training, 20% for evaluation (default)
RL_PORTFOLIO_DATA_SPLIT_RATIO=0.8 python main.py --mode=training

# Use 90% for training, 10% for evaluation (more training data)
RL_PORTFOLIO_DATA_SPLIT_RATIO=0.9 python main.py --mode=training
```

### Output Directory Organization

```bash
# Organize outputs by experiment
mkdir -p experiments/exp_001
RL_PORTFOLIO_OUTPUT_DIR=experiments/exp_001 python main.py --mode=evaluation

# Organize by date
mkdir -p results/$(date +%Y-%m)
RL_PORTFOLIO_OUTPUT_DIR=results/$(date +%Y-%m) python main.py --mode=evaluation
```

## Environment Variable Usage

### Complete Environment Setup

```bash
# Set up comprehensive environment configuration
export RL_PORTFOLIO_MODE=evaluation
export RL_PORTFOLIO_MODEL_PATH=models/best_model.zip
export RL_PORTFOLIO_DATA_SPLIT_RATIO=0.8
export RL_PORTFOLIO_OUTPUT_DIR=results/production
export RL_PORTFOLIO_UNICODE_MODE=unicode

# Run with environment configuration
python main.py
```

### Development vs Production Settings

```bash
# Development environment
export RL_PORTFOLIO_MODE=training
export RL_PORTFOLIO_UNICODE_MODE=unicode
export RL_PORTFOLIO_UNICODE_DEBUG=true
python main.py

# Production environment
export RL_PORTFOLIO_MODE=evaluation
export RL_PORTFOLIO_MODEL_PATH=models/production_model.zip
export RL_PORTFOLIO_OUTPUT_DIR=/var/results
export RL_PORTFOLIO_UNICODE_MODE=ascii
python main.py
```

### Environment File Usage

Create a `.env` file for consistent settings:

```bash
# .env file
RL_PORTFOLIO_MODE=evaluation
RL_PORTFOLIO_MODEL_PATH=models/latest_model.zip
RL_PORTFOLIO_DATA_SPLIT_RATIO=0.8
RL_PORTFOLIO_OUTPUT_DIR=results/latest
RL_PORTFOLIO_UNICODE_MODE=auto
```

Load and use:

```bash
# Load environment and run
set -a; source .env; set +a
python main.py
```

## Integration Examples

### Python Script Integration

```python
#!/usr/bin/env python3
"""
Example integration script for automated model training and evaluation.
"""

import subprocess
import os
import sys
from datetime import datetime
from pathlib import Path

def run_training():
    """Run training mode and return model path."""
    print("Starting training phase...")
    
    # Set environment for training
    env = os.environ.copy()
    env['RL_PORTFOLIO_MODE'] = 'training'
    env['RL_PORTFOLIO_DATA_SPLIT_RATIO'] = '0.8'
    
    # Run training
    result = subprocess.run([sys.executable, 'main.py'], env=env, capture_output=True, text=True)
    
    if result.returncode != 0:
        print(f"Training failed: {result.stderr}")
        return None
    
    # Find the generated model
    models_dir = Path('models')
    model_files = list(models_dir.glob('ppo_portfolio_agent_*.zip'))
    if model_files:
        latest_model = max(model_files, key=lambda p: p.stat().st_mtime)
        print(f"Training completed. Model saved: {latest_model}")
        return str(latest_model)
    
    return None

def run_evaluation(model_path):
    """Run evaluation mode with specified model."""
    print(f"Starting evaluation with model: {model_path}")
    
    # Set environment for evaluation
    env = os.environ.copy()
    env['RL_PORTFOLIO_MODE'] = 'evaluation'
    env['RL_PORTFOLIO_MODEL_PATH'] = model_path
    
    # Run evaluation
    result = subprocess.run([sys.executable, 'main.py'], env=env, capture_output=True, text=True)
    
    if result.returncode != 0:
        print(f"Evaluation failed: {result.stderr}")
        return False
    
    print("Evaluation completed successfully")
    return True

def main():
    """Main integration workflow."""
    # Step 1: Train model
    model_path = run_training()
    if not model_path:
        print("Training failed, aborting workflow")
        sys.exit(1)
    
    # Step 2: Evaluate model
    if not run_evaluation(model_path):
        print("Evaluation failed")
        sys.exit(1)
    
    print("Complete workflow finished successfully")

if __name__ == "__main__":
    main()
```

### Batch Processing Script

```bash
#!/bin/bash
# batch_process.sh - Process multiple configurations

set -e

# Configuration arrays
SPLIT_RATIOS=(0.75 0.8 0.85 0.9 0.95)
EXPERIMENT_NAME="split_ratio_comparison"

# Create experiment directory
mkdir -p experiments/${EXPERIMENT_NAME}

echo "Starting batch processing for ${EXPERIMENT_NAME}"

for ratio in "${SPLIT_RATIOS[@]}"; do
    echo "Processing split ratio: ${ratio}"
    
    # Create subdirectory for this experiment
    exp_dir="experiments/${EXPERIMENT_NAME}/ratio_${ratio}"
    mkdir -p "${exp_dir}"
    
    # Set environment variables
    export RL_PORTFOLIO_DATA_SPLIT_RATIO=${ratio}
    export RL_PORTFOLIO_OUTPUT_DIR="${exp_dir}"
    
    # Run training
    echo "Training with ratio ${ratio}..."
    python main.py --mode=training
    
    # Find the generated model
    latest_model=$(ls -t models/ppo_portfolio_agent_*.zip | head -1)
    
    # Run evaluation
    echo "Evaluating with ratio ${ratio}..."
    python main.py --mode=evaluation --model-path="${latest_model}"
    
    # Move model to experiment directory
    mv "${latest_model}" "${exp_dir}/"
    
    echo "Completed ratio ${ratio}"
done

echo "Batch processing completed"
```

## Automation and Scripting

### Cron Job Setup

```bash
# Add to crontab for monthly execution
# Run on the first day of each month at 2 AM
0 2 1 * * /path/to/rl_portfolio/run_monthly.sh

# run_monthly.sh
#!/bin/bash
cd /path/to/rl_portfolio
export RL_PORTFOLIO_MODE=full
export RL_PORTFOLIO_OUTPUT_DIR=results/monthly/$(date +%Y-%m)
mkdir -p $RL_PORTFOLIO_OUTPUT_DIR
python main.py > $RL_PORTFOLIO_OUTPUT_DIR/execution.log 2>&1
```

### Docker Integration

```dockerfile
# Dockerfile
FROM python:3.9-slim

WORKDIR /app
COPY requirements.txt .
RUN pip install -r requirements.txt

COPY . .

# Set default environment
ENV RL_PORTFOLIO_MODE=evaluation
ENV RL_PORTFOLIO_UNICODE_MODE=ascii

CMD ["python", "main.py"]
```

```bash
# Build and run
docker build -t rl-portfolio .

# Run training mode
docker run -e RL_PORTFOLIO_MODE=training -v $(pwd)/models:/app/models rl-portfolio

# Run evaluation mode
docker run -e RL_PORTFOLIO_MODE=evaluation -v $(pwd)/models:/app/models -v $(pwd)/results:/app/results rl-portfolio
```

### CI/CD Pipeline Example

```yaml
# .github/workflows/model_validation.yml
name: Model Validation

on:
  push:
    branches: [ main ]
  pull_request:
    branches: [ main ]

jobs:
  validate:
    runs-on: ubuntu-latest
    
    steps:
    - uses: actions/checkout@v2
    
    - name: Set up Python
      uses: actions/setup-python@v2
      with:
        python-version: 3.9
    
    - name: Install dependencies
      run: |
        python -m pip install --upgrade pip
        pip install -r requirements.txt
    
    - name: Run training test
      env:
        RL_PORTFOLIO_MODE: training
        RL_PORTFOLIO_DATA_SPLIT_RATIO: 0.8
      run: python main.py
    
    - name: Run evaluation test
      env:
        RL_PORTFOLIO_MODE: evaluation
      run: python main.py
    
    - name: Upload results
      uses: actions/upload-artifact@v2
      with:
        name: test-results
        path: results/
```

## Performance Optimization

### Memory Management

```bash
# Monitor memory usage during execution
python -m memory_profiler main.py --mode=training

# Use memory-efficient settings for large datasets
RL_PORTFOLIO_DATA_SPLIT_RATIO=0.8 python main.py --mode=training
```

### Parallel Processing

```bash
# Run multiple evaluations in parallel
python main.py --mode=evaluation --model-path=models/model1.zip &
python main.py --mode=evaluation --model-path=models/model2.zip &
python main.py --mode=evaluation --model-path=models/model3.zip &
wait
```

### Resource Monitoring

```bash
# Monitor system resources during execution
#!/bin/bash
# monitor_execution.sh

# Start monitoring in background
top -b -d 1 -p $$ > system_usage.log &
MONITOR_PID=$!

# Run the main process
python main.py --mode=full

# Stop monitoring
kill $MONITOR_PID

echo "Execution completed. Check system_usage.log for resource usage."
```

### Optimization Tips

1. **Training Mode Optimization:**
   ```bash
   # Use smaller data split for faster training
   RL_PORTFOLIO_DATA_SPLIT_RATIO=0.75 python main.py --mode=training
   
   # Disable unnecessary logging
   RL_PORTFOLIO_UNICODE_MODE=ascii python main.py --mode=training
   ```

2. **Evaluation Mode Optimization:**
   ```bash
   # Use specific model to avoid auto-detection overhead
   python main.py --mode=evaluation --model-path=models/specific_model.zip
   
   # Custom output directory to avoid conflicts
   RL_PORTFOLIO_OUTPUT_DIR=results/fast_eval python main.py --mode=evaluation
   ```

3. **Full Mode Optimization:**
   ```bash
   # Balance training and evaluation data
   RL_PORTFOLIO_DATA_SPLIT_RATIO=0.8 python main.py --mode=full
   
   # Use efficient Unicode mode
   RL_PORTFOLIO_UNICODE_MODE=safe python main.py --mode=full
   ```

## Common Patterns and Best Practices

### Model Versioning

```bash
# Create versioned models
python main.py --mode=training
latest_model=$(ls -t models/ppo_portfolio_agent_*.zip | head -1)
cp "$latest_model" "models/v$(date +%Y%m%d)_production.zip"
```

### Result Archiving

```bash
# Archive results by date
mkdir -p archive/$(date +%Y-%m)
mv results/*.csv results/*.json results/*.txt archive/$(date +%Y-%m)/
```

### Error Recovery

```bash
# Robust execution with retry logic
#!/bin/bash
max_retries=3
retry_count=0

while [ $retry_count -lt $max_retries ]; do
    if python main.py --mode=evaluation; then
        echo "Execution successful"
        break
    else
        retry_count=$((retry_count + 1))
        echo "Attempt $retry_count failed, retrying..."
        sleep 10
    fi
done

if [ $retry_count -eq $max_retries ]; then
    echo "All attempts failed"
    exit 1
fi
```

This comprehensive usage guide covers all major use cases and integration patterns for the RL Portfolio Rebalancing System's command-line modes.